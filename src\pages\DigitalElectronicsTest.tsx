import React, { useState, useEffect } from 'react';

const QUESTIONS = [
  {
    id: 1,
    question: 'Which logic gate outputs HIGH only when all its inputs are HIGH?',
    options: ['OR', 'AND', 'NAND', 'XOR'],
    answer: 1,
    type: 'mcq',
  },
  {
    id: 2,
    question: 'What is the minimum number of flip-flops required to build a MOD-16 counter?',
    options: ['2', '3', '4', '5'],
    answer: 2,
    type: 'mcq',
  },
  {
    id: 3,
    question: 'Which of the following is a combinational circuit?',
    options: ['Counter', 'Shift Register', 'Multiplexer', 'Flip-Flop'],
    answer: 2,
    type: 'mcq',
  },
  {
    id: 4,
    question: 'The output of a JK flip-flop with J=1, K=1, and clock pulse is?',
    options: ['No Change', 'Set', 'Reset', 'Toggle'],
    answer: 3,
    type: 'mcq',
  },
  {
    id: 5,
    question: 'Which device is used to store one bit of data?',
    options: ['Multiplexer', 'Flip-Flop', 'Encoder', 'Decoder'],
    answer: 1,
    type: 'mcq',
  },
  {
    id: 6,
    question: 'Write a function to compute the sum of two numbers. The function should be named sum and take two arguments a and b.',
    type: 'coding',
    starterCode: 'def sum(a, b):\n    # Write your code here\n    ',
    expectedOutput: '5',
  },
];

const TEST_DURATION_SECONDS = 5 * 60; // 5 minutes for demo

const DigitalElectronicsTest: React.FC = () => {
  const [timeLeft, setTimeLeft] = useState(TEST_DURATION_SECONDS);
  const [currentQ, setCurrentQ] = useState(0);
  const [answers, setAnswers] = useState<(number | null)[]>(Array(QUESTIONS.length).fill(null));
  const [codeAnswers, setCodeAnswers] = useState<string[]>(Array(QUESTIONS.length).fill(''));
  const [runOutput, setRunOutput] = useState<string>('');
  const [isRunning, setIsRunning] = useState(false);

  useEffect(() => {
    if (timeLeft <= 0) return;
    const timer = setInterval(() => setTimeLeft((t) => t - 1), 1000);
    return () => clearInterval(timer);
  }, [timeLeft]);

  const handleOptionClick = (idx: number) => {
    const newAnswers = [...answers];
    newAnswers[currentQ] = idx;
    setAnswers(newAnswers);
  };

  const handleCodeChange = (val: string) => {
    const newCodes = [...codeAnswers];
    newCodes[currentQ] = val;
    setCodeAnswers(newCodes);
  };

  // Simulate code execution for demo
  const handleRunCode = () => {
    setIsRunning(true);
    setTimeout(() => {
      // For demo, just check if the code contains 'return a + b' or 'a+b'
      const code = codeAnswers[currentQ];
      if (/return\s+a\s*\+\s*b/.test(code) || /a\s*\+\s*b/.test(code)) {
        setRunOutput('5');
      } else {
        setRunOutput('Wrong output');
      }
      setIsRunning(false);
    }, 800);
  };

  const formatTime = (sec: number) => {
    const m = Math.floor(sec / 60).toString().padStart(2, '0');
    const s = (sec % 60).toString().padStart(2, '0');
    return `${m}:${s}`;
  };

  const isCodingQ = QUESTIONS[currentQ].type === 'coding';

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100 flex">
      {/* Sidebar */}
      <div className="w-64 bg-gray-800 border-r border-gray-700 flex flex-col p-6">
        <h2 className="text-xl font-bold mb-6 text-blue-400">Questions</h2>
        <div className="flex flex-col gap-3 flex-1">
          {QUESTIONS.map((q, idx) => (
            <button
              key={q.id}
              onClick={() => setCurrentQ(idx)}
              className={`w-full text-left px-4 py-2 rounded-lg font-medium transition-colors
                ${currentQ === idx ? 'bg-blue-700 text-white' : ''}
                ${q.type === 'coding' && codeAnswers[idx] ? 'bg-green-700 text-white' : ''}
                ${q.type === 'mcq' && answers[idx] !== null ? 'bg-green-700 text-white' : 'bg-gray-700 text-gray-300'}
              `}
            >
              Q{idx + 1}: {q.type === 'coding' ? (codeAnswers[idx] ? 'Answered' : 'Unanswered') : (answers[idx] !== null ? 'Answered' : 'Unanswered')}
            </button>
          ))}
        </div>
        <div className="mt-8 text-sm text-gray-400">Problems: {QUESTIONS.length}</div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col p-8">
        {/* Contest Name, Timer, and Submit Button at Top */}
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-2xl font-bold text-blue-400">Digital Electronics Mastery - Sample Test</h1>
          <div className="flex items-center gap-4">
            <div className="bg-gray-800 px-4 py-2 rounded-lg text-lg font-mono">
              ⏰ {formatTime(timeLeft)}
            </div>
            <button
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold"
              onClick={() => alert(`You answered ${answers.filter(a => a !== null).length + codeAnswers.filter(c => c.trim()).length} out of ${QUESTIONS.length} questions.`)}
              disabled={timeLeft <= 0}
            >
              Submit
            </button>
          </div>
        </div>
        {/* Question and Options or Coding Editor */}
        {isCodingQ ? (
          <div className="flex w-full max-w-4xl mx-auto h-[500px] border border-gray-700 rounded-xl overflow-hidden shadow-lg">
            {/* Left: Question Description */}
            <div className="w-1/2 bg-gray-800 p-6 flex flex-col justify-between border-r border-gray-700">
              <div>
                <div className="text-lg font-bold text-white mb-4">Q{currentQ + 1}. Coding Question</div>
                <div className="text-gray-200 mb-6">{QUESTIONS[currentQ].question}</div>
              </div>
            </div>
            {/* Right: IDE */}
            <div className="w-1/2 bg-gray-900 p-6 flex flex-col">
              <textarea
                className="w-full h-48 bg-gray-800 text-gray-100 font-mono rounded-lg p-4 mb-4 border border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={codeAnswers[currentQ] || QUESTIONS[currentQ].starterCode}
                onChange={e => handleCodeChange(e.target.value)}
                disabled={timeLeft <= 0}
              />
              <div className="flex gap-3 mb-4">
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold"
                  onClick={handleRunCode}
                  disabled={isRunning || timeLeft <= 0}
                >
                  {isRunning ? 'Running...' : 'Run'}
                </button>
              </div>
              <div className="bg-gray-800 rounded-lg p-4 border border-gray-700 text-green-400 flex-1">
                Output: {runOutput}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex flex-col justify-center">
            <div className="w-full max-w-2xl mx-auto">
              <div className="text-xl font-bold text-white mb-6">
                Q{currentQ + 1}. {QUESTIONS[currentQ].question}
              </div>
              <div className="grid grid-cols-1 gap-4 mb-6">
                {QUESTIONS[currentQ]?.options && QUESTIONS[currentQ]?.options?.map((opt, idx) => (
                  <button
                    key={idx}
                    onClick={() => handleOptionClick(idx)}
                    className={`w-full text-left px-6 py-3 rounded-lg border transition-colors font-medium
                      ${answers[currentQ] === idx ? 'bg-blue-600 border-blue-400 text-white' : 'bg-gray-800 border-gray-700 text-gray-200 hover:bg-gray-700'}
                    `}
                    disabled={timeLeft <= 0}
                  >
                    {opt}
                  </button>
                ))}
              </div>
              <div className="flex justify-between mt-4">
                <button
                  className="bg-gray-700 hover:bg-gray-600 text-gray-200 px-4 py-2 rounded-lg font-medium"
                  onClick={() => setCurrentQ((q) => Math.max(0, q - 1))}
                  disabled={currentQ === 0}
                >
                  Previous
                </button>
                <button
                  className="bg-gray-700 hover:bg-gray-600 text-gray-200 px-4 py-2 rounded-lg font-medium"
                  onClick={() => setCurrentQ((q) => Math.min(QUESTIONS.length - 1, q + 1))}
                  disabled={currentQ === QUESTIONS.length - 1}
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DigitalElectronicsTest; 