# ElectronicBit

**Let's Learn Electronics Bit by Bit**

A comprehensive platform for semiconductor interview preparation and electronics learning.

## 🚀 Features

- **Practice Problems**: Access a vast collection of semiconductor and electronics problems with detailed solutions
- **Contest Mode**: Compete with other students in timed contests to test your knowledge
- **Learning Resources**: Comprehensive study materials covering all aspects of electronics and semiconductor theory
- **Community**: Connect with fellow electronics enthusiasts and share knowledge
- **Progress Tracking**: Monitor your learning progress with detailed analytics and performance metrics
- **Certifications**: Earn certificates upon completing different modules and contests

## 🛠️ Tech Stack

- **Frontend**: React 19 with TypeScript
- **Styling**: Tailwind CSS with custom dark theme
- **Icons**: Lucide React
- **Routing**: React Router DOM
- **Build Tool**: Create React App

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd electronicbit
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

4. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## 🎨 Design Features

- **Dark Mode**: Complete dark theme optimized for long study sessions
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Modern UI**: Clean, intuitive interface with smooth animations
- **Accessibility**: Built with accessibility best practices

## 📱 Pages

### Home
- Hero section with call-to-action buttons
- Feature highlights
- Statistics showcase
- Engaging content about the platform

### Practice
- Filterable practice problems by category and difficulty
- Detailed problem descriptions
- Time estimates and success metrics
- Interactive problem cards

### Contest
- Upcoming, ongoing, and completed contests
- Contest details including prizes, participants, and topics
- Registration and participation functionality
- Real-time contest status

### About Us
- Company mission and values
- Team member profiles
- Contact information
- Platform statistics

### Login/Registration
- Comprehensive registration form with all required fields:
  - Full Name
  - College/University Name
  - Phone Number
  - Password (with show/hide functionality)
  - Resume/CV upload
- Google OAuth integration
- Form validation and error handling

## 🎯 Target Audience

- Electronics engineering students
- Semiconductor industry professionals
- Interview preparation candidates
- Electronics enthusiasts and hobbyists
- Academic institutions and educators

## 🔧 Development

### Project Structure
```
src/
├── components/     # Reusable UI components
├── pages/         # Page components
├── App.tsx        # Main app component
└── index.css      # Global styles and Tailwind imports
```

### Key Components
- **Header**: Navigation with responsive mobile menu
- **Login Form**: Complete registration with file upload
- **Practice Cards**: Interactive problem display
- **Contest Cards**: Contest information and registration
- **About Sections**: Company information and team profiles

## 🚀 Deployment

To build the project for production:

```bash
npm run build
```

This creates an optimized production build in the `build` folder.

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📞 Contact

- Email: <EMAIL>
- Support: <EMAIL>
- Office: San Francisco, CA

---

**ElectronicBit** - Empowering electronics education through interactive learning and community engagement.
