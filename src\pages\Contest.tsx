import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Trophy, Clock, Users, Calendar, Award, Play, Star } from 'lucide-react';

const Contest: React.FC = () => {
  const [selectedFilter, setSelectedFilter] = useState('all');

  const filters = [
    { id: 'all', name: 'All Contests' },
    { id: 'upcoming', name: 'Upcoming' },
    { id: 'ongoing', name: 'Ongoing' },
    { id: 'completed', name: 'Completed' },
  ];

  const contests = [
    {
      id: 1,
      title: 'Semiconductor Physics Challenge',
      status: 'upcoming',
      startDate: '2024-02-15T10:00:00',
      duration: '2 hours',
      participants: 150,
      maxParticipants: 200,
      prize: '$500',
      difficulty: 'Medium',
      topics: ['PN Junction', 'BJT', 'MOSFET'],
      description: 'Test your knowledge of semiconductor physics with challenging problems covering diodes, transistors, and more.',
    },
    {
      id: 2,
      title: 'Digital Electronics Mastery',
      status: 'ongoing',
      startDate: '2024-02-10T14:00:00',
      duration: '3 hours',
      participants: 180,
      maxParticipants: 200,
      prize: '$750',
      difficulty: 'Hard',
      topics: ['Logic Gates', 'Sequential Circuits', 'Memory'],
      description: 'Advanced digital electronics contest focusing on sequential circuits, memory systems, and complex logic design.',
    },
    {
      id: 3,
      title: 'Analog Circuit Design',
      status: 'upcoming',
      startDate: '2024-02-20T09:00:00',
      duration: '2.5 hours',
      participants: 120,
      maxParticipants: 150,
      prize: '$400',
      difficulty: 'Medium',
      topics: ['Op-Amps', 'Filters', 'Oscillators'],
      description: 'Design and analyze analog circuits including operational amplifiers, active filters, and oscillators.',
    },
    {
      id: 4,
      title: 'Microprocessor Architecture',
      status: 'completed',
      startDate: '2024-02-05T11:00:00',
      duration: '4 hours',
      participants: 95,
      maxParticipants: 100,
      prize: '$600',
      difficulty: 'Hard',
      topics: ['8085', '8086', 'Assembly Programming'],
      description: 'Comprehensive test on microprocessor architecture, assembly programming, and system design.',
    },
    {
      id: 5,
      title: 'Communication Systems',
      status: 'upcoming',
      startDate: '2024-02-25T13:00:00',
      duration: '2 hours',
      participants: 80,
      maxParticipants: 120,
      prize: '$300',
      difficulty: 'Easy',
      topics: ['AM/FM', 'Modulation', 'Demodulation'],
      description: 'Basic to intermediate level contest on analog and digital communication systems.',
    },
    {
      id: 6,
      title: 'VLSI Design Fundamentals',
      status: 'upcoming',
      startDate: '2024-03-01T10:00:00',
      duration: '3.5 hours',
      participants: 60,
      maxParticipants: 100,
      prize: '$800',
      difficulty: 'Hard',
      topics: ['CMOS', 'Layout', 'Timing'],
      description: 'Advanced contest covering VLSI design principles, CMOS circuits, and timing analysis.',
    },
  ];

  const filteredContests = contests.filter(contest => {
    if (selectedFilter === 'all') return true;
    return contest.status === selectedFilter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'text-blue-400';
      case 'ongoing': return 'text-green-400';
      case 'completed': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'text-green-400';
      case 'Medium': return 'text-yellow-400';
      case 'Hard': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Contests</h1>
          <p className="text-gray-300">Compete with other electronics enthusiasts and win exciting prizes</p>
        </div>

        {/* Filters */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            {filters.map((filter) => (
              <button
                key={filter.id}
                onClick={() => setSelectedFilter(filter.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                  selectedFilter === filter.id
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {filter.name}
              </button>
            ))}
          </div>
        </div>

        {/* Contests Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredContests.map((contest) => (
            <div key={contest.id} className="card hover:bg-gray-700 transition-colors duration-200">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-semibold text-white">{contest.title}</h3>
                <div className="flex items-center space-x-2">
                  <span className={`text-sm font-medium ${getStatusColor(contest.status)}`}>
                    {contest.status.charAt(0).toUpperCase() + contest.status.slice(1)}
                  </span>
                  <span className={`text-sm font-medium ${getDifficultyColor(contest.difficulty)}`}>
                    {contest.difficulty}
                  </span>
                </div>
              </div>

              <p className="text-gray-300 mb-4">{contest.description}</p>

              {/* Contest Details */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(contest.startDate)}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <Clock className="w-4 h-4" />
                  <span>{contest.duration}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <Users className="w-4 h-4" />
                  <span>{contest.participants}/{contest.maxParticipants} participants</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <Award className="w-4 h-4" />
                  <span>{contest.prize} prize</span>
                </div>
              </div>

              {/* Topics */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {contest.topics.map((topic, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded-md"
                    >
                      {topic}
                    </span>
                  ))}
                </div>
              </div>

              {/* Action Button */}
              {contest.title === 'Digital Electronics Mastery' && contest.status === 'ongoing' ? (
                <Link
                  to="/contest/digital-electronics-mastery/test"
                  className="w-full py-2 px-4 rounded-lg font-medium transition-colors duration-200 bg-green-600 hover:bg-green-700 text-white flex items-center justify-center space-x-2"
                >
                  <Play className="w-4 h-4" />
                  <span>Join Now</span>
                </Link>
              ) : (
                <button
                  className={`w-full py-2 px-4 rounded-lg font-medium transition-colors duration-200 ${
                    contest.status === 'ongoing'
                      ? 'bg-green-600 hover:bg-green-700 text-white'
                      : contest.status === 'upcoming'
                      ? 'btn-primary'
                      : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  }`}
                  disabled={contest.status === 'completed'}
                >
                  {contest.status === 'ongoing' ? (
                    <span className="flex items-center justify-center space-x-2">
                      <Play className="w-4 h-4" />
                      <span>Join Now</span>
                    </span>
                  ) : contest.status === 'upcoming' ? (
                    <span className="flex items-center justify-center space-x-2">
                      <Trophy className="w-4 h-4" />
                      <span>Register</span>
                    </span>
                  ) : (
                    <span className="flex items-center justify-center space-x-2">
                      <Star className="w-4 h-4" />
                      <span>Completed</span>
                    </span>
                  )}
                </button>
              )}
            </div>
          ))}
        </div>

        {filteredContests.length === 0 && (
          <div className="text-center py-12">
            <Trophy className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-300 mb-2">No contests found</h3>
            <p className="text-gray-400">Check back later for new contests or try a different filter.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Contest; 