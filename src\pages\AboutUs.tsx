import React from 'react';
import { Users, Target, Award, BookOpen, Zap, Heart } from 'lucide-react';

const AboutUs: React.FC = () => {
  const stats = [
    { icon: Users, value: '500+', label: 'Active Students' },
    { icon: Target, value: '1000+', label: 'Practice Problems' },
    { icon: Award, value: '50+', label: 'Contests Completed' },
    { icon: BookOpen, value: '100+', label: 'Learning Resources' },
  ];

  const team = [
    {
      name: 'Dr. <PERSON>',
      role: 'Founder & Lead Instructor',
      bio: 'PhD in Electrical Engineering with 15+ years of experience in semiconductor design and education.',
      expertise: ['Semiconductor Physics', 'VLSI Design', 'Digital Electronics'],
    },
    {
      name: 'Prof. <PERSON>',
      role: 'Technical Director',
      bio: 'Professor of Electronics with expertise in analog circuit design and microprocessor architecture.',
      expertise: ['Analog Electronics', 'Microprocessors', 'Circuit Design'],
    },
    {
      name: 'Dr. <PERSON><PERSON>',
      role: 'Content Lead',
      bio: 'Specialist in communication systems and digital signal processing with industry experience.',
      expertise: ['Communication Systems', 'DSP', 'Digital Electronics'],
    },
    {
      name: '<PERSON>',
      role: 'Platform Developer',
      bio: 'Full-stack developer passionate about creating intuitive learning experiences for electronics students.',
      expertise: ['Web Development', 'UI/UX Design', 'Educational Technology'],
    },
  ];

  const values = [
    {
      icon: Zap,
      title: 'Innovation',
      description: 'We continuously innovate our platform to provide the best learning experience for electronics students.',
    },
    {
      icon: Target,
      title: 'Excellence',
      description: 'We strive for excellence in everything we do, from content quality to user experience.',
    },
    {
      icon: Users,
      title: 'Community',
      description: 'We believe in building a strong community of electronics enthusiasts who support each other.',
    },
    {
      icon: Heart,
      title: 'Passion',
      description: 'Our passion for electronics drives us to create the most comprehensive learning platform.',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            About <span className="text-primary-400">ElectronicBit</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            We're passionate about making electronics education accessible, engaging, and effective. 
            Our platform is designed to help students master semiconductor concepts bit by bit.
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className="flex justify-center mb-2">
                    <Icon className="w-8 h-8 text-primary-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">{stat.value}</div>
                  <div className="text-gray-400">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-white mb-6">Our Mission</h2>
              <p className="text-lg text-gray-300 mb-6">
                At ElectronicBit, we believe that every student deserves access to high-quality electronics education. 
                Our mission is to democratize semiconductor and electronics learning through interactive practice problems, 
                engaging contests, and comprehensive study materials.
              </p>
              <p className="text-lg text-gray-300 mb-6">
                We understand the challenges that electronics students face - complex theories, abstract concepts, 
                and the need for hands-on practice. That's why we've created a platform that combines theoretical 
                knowledge with practical problem-solving.
              </p>
              <p className="text-lg text-gray-300">
                Whether you're preparing for interviews, exams, or simply want to deepen your understanding of 
                electronics, ElectronicBit is your comprehensive learning companion.
              </p>
            </div>
            <div className="card">
              <h3 className="text-xl font-semibold text-white mb-4">What We Offer</h3>
              <ul className="space-y-3 text-gray-300">
                <li className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary-400 rounded-full"></div>
                  <span>Comprehensive practice problems with detailed solutions</span>
                </li>
                <li className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary-400 rounded-full"></div>
                  <span>Timed contests to test your knowledge under pressure</span>
                </li>
                <li className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary-400 rounded-full"></div>
                  <span>Progressive learning paths from basic to advanced concepts</span>
                </li>
                <li className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary-400 rounded-full"></div>
                  <span>Real-time performance tracking and analytics</span>
                </li>
                <li className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary-400 rounded-full"></div>
                  <span>Community features to connect with fellow learners</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-white mb-4">Our Values</h2>
            <p className="text-xl text-gray-300">The principles that guide everything we do</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const Icon = value.icon;
              return (
                <div key={index} className="card text-center">
                  <div className="flex justify-center mb-4">
                    <Icon className="w-12 h-12 text-primary-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">{value.title}</h3>
                  <p className="text-gray-300">{value.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-white mb-4">Meet Our Team</h2>
            <p className="text-xl text-gray-300">The experts behind ElectronicBit</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="card text-center">
                <div className="w-20 h-20 bg-primary-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl font-bold text-white">
                    {member.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-1">{member.name}</h3>
                <p className="text-primary-400 text-sm mb-3">{member.role}</p>
                <p className="text-gray-300 text-sm mb-4">{member.bio}</p>
                <div className="flex flex-wrap gap-1 justify-center">
                  {member.expertise.map((skill, skillIndex) => (
                    <span
                      key={skillIndex}
                      className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded-md"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">Get in Touch</h2>
          <p className="text-xl text-gray-300 mb-8">
            Have questions or suggestions? We'd love to hear from you!
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="card text-center">
              <h3 className="text-lg font-semibold text-white mb-2">Email</h3>
              <p className="text-primary-400"><EMAIL></p>
            </div>
            <div className="card text-center">
              <h3 className="text-lg font-semibold text-white mb-2">Support</h3>
              <p className="text-primary-400"><EMAIL></p>
            </div>
            <div className="card text-center">
              <h3 className="text-lg font-semibold text-white mb-2">Office</h3>
              <p className="text-gray-300">San Francisco, CA</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutUs; 