import React, { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { Cpu, Trophy, BookOpen, Users, Target, Award } from 'lucide-react';
import reviewGif from '../assets/review.gif';

const testimonials = [
  {
    name: '<PERSON><PERSON>',
    role: 'VLSI Engineer @ Intel',
    avatar: '', // You can add image URLs here
    color: 'from-blue-600 to-purple-600',
    text: "ElectronicBit's practice problems and contests helped me ace my campus interviews. The explanations are clear and the platform is super intuitive!",
  },
  {
    name: '<PERSON><PERSON>',
    role: 'Student @ IIT Bombay',
    avatar: '',
    color: 'from-green-600 to-teal-600',
    text: 'The structured learning path and real-world problems made my preparation much more effective. Highly recommended for anyone in electronics!',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    role: 'Design Intern @ Qualcomm',
    avatar: '',
    color: 'from-orange-500 to-yellow-500',
    text: 'I loved the community and the detailed solutions. The platform feels modern and the analytics helped me track my progress.',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    role: 'Verification Engineer @ AMD',
    avatar: '',
    color: 'from-pink-500 to-red-500',
    text: 'The best platform for semiconductor interview prep! The problems are challenging and the UI is fantastic.',
  },
];

const Home: React.FC = () => {
  const features = [
    {
      icon: Cpu,
      title: 'Practice Problems',
      description: 'Access a vast collection of semiconductor and electronics problems with detailed solutions.',
    },
    {
      icon: Trophy,
      title: 'Contest Mode',
      description: 'Compete with other students in timed contests to test your knowledge.',
    },
    {
      icon: BookOpen,
      title: 'Learning Resources',
      description: 'Comprehensive study materials covering all aspects of electronics and semiconductor theory.',
    },
    {
      icon: Users,
      title: 'Community',
      description: 'Connect with fellow electronics enthusiasts and share knowledge.',
    },
    {
      icon: Target,
      title: 'Track Progress',
      description: 'Monitor your learning progress with detailed analytics and performance metrics.',
    },
    {
      icon: Award,
      title: 'Certifications',
      description: 'Earn certificates upon completing different modules and contests.',
    },
  ];

  const [current, setCurrent] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      setCurrent((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => { if (intervalRef.current) clearInterval(intervalRef.current); };
  }, []);

  const goTo = (idx: number) => {
    setCurrent(idx);
    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = setInterval(() => {
      setCurrent((prev) => (prev + 1) % testimonials.length);
    }, 5000);
  };

  const prev = () => goTo((current - 1 + testimonials.length) % testimonials.length);
  const next = () => goTo((current + 1) % testimonials.length);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative gradient-bg py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Master Electronics
              <span className="gradient-text block">Bit by Bit</span>
            </h1>
            <p className="text-xl text-slate-300 mb-8 max-w-3xl mx-auto">
              Your comprehensive platform for semiconductor interview preparation. 
              Practice problems, join contests, and excel in your electronics career.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/practice"
                className="btn-primary text-lg px-8 py-3 flex items-center justify-center space-x-2"
              >
                <Cpu className="w-5 h-5" />
                <span>Start Practicing</span>
              </Link>
              <Link
                to="/login"
                className="btn-accent text-lg px-8 py-3 flex items-center justify-center space-x-2"
              >
                <Trophy className="w-5 h-5" />
                <span>Join Contest</span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Platform Demo Section */}
      <section className="py-20 bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Experience Our Platform
            </h2>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto">
              See how ElectronicBit helps you master electronics through interactive learning and practice.
            </p>
          </div>
          <div className="flex justify-center">
            <div className="relative">
              <img 
                src={reviewGif} 
                alt="ElectronicBit Platform Demo" 
                className="rounded-xl shadow-2xl border border-slate-700 max-w-full h-auto"
                style={{ maxHeight: '600px' }}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-slate-800/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Why Choose ElectronicBit?
            </h2>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto">
              Comprehensive tools and resources designed specifically for electronics and semiconductor interview preparation.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="card card-hover">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mr-4 shadow-lg">
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-white">{feature.title}</h3>
                  </div>
                  <p className="text-slate-300">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold gradient-text mb-2">1000+</div>
              <div className="text-slate-300">Practice Problems</div>
            </div>
            <div>
              <div className="text-4xl font-bold gradient-text mb-2">500+</div>
              <div className="text-slate-300">Active Students</div>
            </div>
            <div>
              <div className="text-4xl font-bold gradient-text mb-2">50+</div>
              <div className="text-slate-300">Contests Completed</div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section - Carousel */}
      <section className="py-20 bg-slate-800/70">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              What do our subscribers say?
            </h2>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto">
              Hear from learners and professionals who have used ElectronicBit to boost their electronics and semiconductor skills.
            </p>
          </div>
          <div className="relative flex items-center justify-center">
            {/* Left Arrow */}
            <button
              className="absolute left-0 z-10 p-2 rounded-full hover:bg-slate-700/60 transition"
              onClick={prev}
              aria-label="Previous testimonial"
            >
              <svg width="36" height="36" fill="none" viewBox="0 0 24 24"><path stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7"/></svg>
            </button>
            {/* Carousel Cards */}
            <div className="flex w-full justify-center items-center" style={{ minHeight: 320 }}>
              {testimonials.map((t, idx) => {
                // Center card
                if (idx === current) {
                  return (
                    <div
                      key={idx}
                      className="bg-slate-900/80 border border-slate-700 rounded-xl p-8 shadow-2xl flex flex-col items-center mx-4 z-20 transition-all duration-500 w-full max-w-xl scale-105"
                      style={{ opacity: 1 }}
                    >
                      <div className={`w-20 h-20 rounded-full bg-gradient-to-br ${t.color} flex items-center justify-center text-white font-bold text-3xl mb-4 overflow-hidden border-4 border-white/10`}>{t.avatar ? <img src={t.avatar} alt={t.name} className="w-full h-full object-cover rounded-full" /> : t.name[0]}</div>
                      <div className="text-xl font-semibold text-white mb-1">{t.name}</div>
                      <div className="text-slate-400 text-base mb-2">{t.role}</div>
                      <div className="flex-1">
                        <p className="text-slate-200 text-lg mb-2 text-center">“{t.text}”</p>
                      </div>
                    </div>
                  );
                }
                // Left/Right blurred cards
                if (idx === (current - 1 + testimonials.length) % testimonials.length || idx === (current + 1) % testimonials.length) {
                  return (
                    <div
                      key={idx}
                      className="bg-slate-900/60 border border-slate-700 rounded-xl p-8 shadow-lg flex flex-col items-center mx-4 z-10 transition-all duration-500 w-full max-w-md scale-95 blur-sm opacity-60"
                      style={{ pointerEvents: 'none' }}
                    >
                      <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${t.color} flex items-center justify-center text-white font-bold text-2xl mb-4 overflow-hidden border-4 border-white/10`}>{t.avatar ? <img src={t.avatar} alt={t.name} className="w-full h-full object-cover rounded-full" /> : t.name[0]}</div>
                      <div className="text-lg font-semibold text-white mb-1">{t.name}</div>
                      <div className="text-slate-400 text-sm mb-2">{t.role}</div>
                      <div className="flex-1">
                        <p className="text-slate-200 text-base mb-2 text-center">“{t.text}”</p>
                      </div>
                    </div>
                  );
                }
                // Hide other cards
                return null;
              })}
            </div>
            {/* Right Arrow */}
            <button
              className="absolute right-0 z-10 p-2 rounded-full hover:bg-slate-700/60 transition"
              onClick={next}
              aria-label="Next testimonial"
            >
              <svg width="36" height="36" fill="none" viewBox="0 0 24 24"><path stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7"/></svg>
            </button>
          </div>
          {/* Pagination Dots */}
          <div className="flex justify-center mt-6 space-x-2">
            {testimonials.map((_, idx) => (
              <button
                key={idx}
                className={`w-3 h-3 rounded-full ${idx === current ? 'bg-white' : 'bg-slate-500'} transition-all`}
                onClick={() => goTo(idx)}
                aria-label={`Go to testimonial ${idx + 1}`}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Footer Section */}
      <footer className="bg-slate-900 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* About Section */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">About</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Legal</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Referral Policy</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Privacy</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Contact Us</a></li>
                <li><a href="#" className="text-slate-400 hover:text-white transition-colors">Cancellation and Refund</a></li>
              </ul>
            </div>

            {/* Reach us at Section */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Reach us at</h3>
              <div className="flex space-x-4">
                <a href="#" className="text-slate-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                <a href="#" className="text-slate-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                  </svg>
                </a>
                <a href="#" className="text-slate-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </a>
                <a href="#" className="text-slate-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
                <a href="#" className="text-slate-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 5.457v13.909c0 .904-.732 1.636-1.636 1.636h-3.819V11.73L12 16.64l-6.545-4.91v9.273H1.636A1.636 1.636 0 0 1 0 19.366V5.457c0-.904.732-1.636 1.636-1.636h20.728c.904 0 1.636.732 1.636 1.636zM7.909 13.909L12 16.64l4.091-2.73V11.73L12 14.64l-4.091-2.91v2.18z"/>
                  </svg>
                </a>
              </div>
            </div>

            {/* ElectronicBit Section */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">ElectronicBit</h3>
              <div className="space-y-2 text-slate-400">
                <p className="text-sm">Registered Office</p>
                <p className="text-sm">123 Electronics Street, Tech City, India - 123456</p>
                <p className="text-sm">CIN: U12345IN2024PTC123456</p>
                <p className="text-sm">Email: <EMAIL></p>
                <p className="text-sm">Phone: +91-9876543210</p>
              </div>
            </div>
          </div>
          
          {/* Copyright */}
          <div className="border-t border-slate-800 mt-8 pt-8 text-center">
            <p className="text-slate-400 text-sm">
              © 2024 ElectronicBit. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home; 