import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Cpu, BookOpen, Target, Clock, Star, Code, CircuitBoard, FileCode } from 'lucide-react';
import CourseStructure from '../components/CourseStructure';

const Practice: React.FC = () => {
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);

  const topics = [
    {
      id: 'computer-architecture',
      name: 'Computer Architecture',
      icon: Cpu,
      description: 'Learn about CPU design, memory hierarchy, pipelining, and computer organization principles.',
      problemCount: 150,
      avgTime: '25 min',
      difficulty: 'Medium',
      color: 'from-blue-600 to-cyan-600',
    },
    {
      id: 'digital-design',
      name: 'Digital Design',
      icon: CircuitBoard,
      description: 'Master digital logic design, combinational and sequential circuits, and state machines.',
      problemCount: 200,
      avgTime: '30 min',
      difficulty: 'Hard',
      color: 'from-purple-600 to-pink-600',
    },
    {
      id: 'verilog-practice',
      name: 'Verilog Practice',
      icon: Code,
      description: 'Practice Verilog HDL coding, RTL design, testbenches, and synthesis concepts.',
      problemCount: 180,
      avgTime: '35 min',
      difficulty: 'Hard',
      color: 'from-green-600 to-emerald-600',
    },
    {
      id: 'scripting',
      name: 'Scripting',
      icon: FileCode,
      description: 'Learn Python, TCL, and shell scripting for automation and tool integration.',
      problemCount: 120,
      avgTime: '20 min',
      difficulty: 'Easy',
      color: 'from-orange-600 to-red-600',
    },
  ];

  // Computer Architecture Course Structure (Tree Style)
  const computerArchitectureLevels = [
    {
      id: 'level-1',
      name: 'Fundamentals',
      description: 'Basic computer organization and number systems',
      isUnlocked: true,
      isCompleted: false,
      subtopics: [
        {
          id: 'time-complexity',
          name: 'Time Complexity',
          articles: 6,
          problems: 15,
          topicStrength: 25,
          isUnlocked: true,
          isCompleted: false,
          icon: '⏱️',
          color: 'bg-blue-600',
          problemsList: [
            {
              id: 'tc-1',
              title: 'Big O Notation Basics',
              description: 'Understand time complexity analysis and Big O notation',
              timeEstimate: '15 min',
              difficulty: 'Easy' as const,
              isCompleted: false,
              isLocked: false,
              type: 'practice' as const,
            },
            {
              id: 'tc-2',
              title: 'Algorithm Analysis',
              description: 'Analyze time complexity of basic algorithms',
              timeEstimate: '20 min',
              difficulty: 'Medium' as const,
              isCompleted: false,
              isLocked: false,
              type: 'practice' as const,
            },
          ],
        },
      ],

    },
    {
      id: 'level-2',
      name: 'Intermediate Concepts',
      description: 'Advanced data structures and algorithms',
      isUnlocked: false,
      isCompleted: false,
      subtopics: [
        {
          id: 'arrays',
          name: 'Arrays',
          articles: 12,
          problems: 44,
          topicStrength: 88,
          isUnlocked: false,
          isCompleted: false,
          icon: '📊',
          color: 'bg-purple-600',
          problemsList: [
            {
              id: 'arr-1',
              title: 'Array Traversal',
              description: 'Learn array manipulation and traversal techniques',
              timeEstimate: '15 min',
              difficulty: 'Easy' as const,
              isCompleted: false,
              isLocked: true,
              type: 'practice' as const,
            },
            {
              id: 'arr-2',
              title: 'Array Sorting',
              description: 'Implement and analyze sorting algorithms',
              timeEstimate: '25 min',
              difficulty: 'Medium' as const,
              isCompleted: false,
              isLocked: true,
              type: 'practice' as const,
            },
          ],
        },
        {
          id: 'math',
          name: 'Math',
          articles: 6,
          problems: 29,
          topicStrength: 77,
          isUnlocked: false,
          isCompleted: false,
          icon: '🧮',
          color: 'bg-green-600',
          problemsList: [
            {
              id: 'math-1',
              title: 'Mathematical Concepts',
              description: 'Learn mathematical foundations for computer architecture',
              timeEstimate: '20 min',
              difficulty: 'Medium' as const,
              isCompleted: false,
              isLocked: true,
              type: 'practice' as const,
            },
            {
              id: 'math-2',
              title: 'Number Systems',
              description: 'Master binary, decimal, and hexadecimal conversions',
              timeEstimate: '25 min',
              difficulty: 'Medium' as const,
              isCompleted: false,
              isLocked: true,
              type: 'practice' as const,
            },
          ],
        },
      ],

    },
    {
      id: 'level-3',
      name: 'Advanced Topics',
      description: 'Complex data structures and advanced algorithms',
      isUnlocked: false,
      isCompleted: false,
      subtopics: [
        {
          id: 'binary-search',
          name: 'Binary Search',
          articles: 5,
          problems: 16,
          topicStrength: 45,
          isUnlocked: false,
          isCompleted: false,
          icon: '🔍',
          color: 'bg-orange-600',
          problemsList: [
            {
              id: 'bs-1',
              title: 'Binary Search Implementation',
              description: 'Implement binary search algorithm',
              timeEstimate: '20 min',
              difficulty: 'Medium' as const,
              isCompleted: false,
              isLocked: true,
              type: 'practice' as const,
            },
            {
              id: 'bs-2',
              title: 'Binary Search Variations',
              description: 'Solve binary search variations and applications',
              timeEstimate: '25 min',
              difficulty: 'Hard' as const,
              isCompleted: false,
              isLocked: true,
              type: 'practice' as const,
            },
          ],
        },
        {
          id: 'strings',
          name: 'Strings',
          articles: 3,
          problems: 33,
          topicStrength: 62,
          isUnlocked: false,
          isCompleted: false,
          icon: '📝',
          color: 'bg-red-600',
          problemsList: [
            {
              id: 'str-1',
              title: 'String Manipulation',
              description: 'Learn string processing techniques',
              timeEstimate: '20 min',
              difficulty: 'Medium' as const,
              isCompleted: false,
              isLocked: true,
              type: 'practice' as const,
            },
            {
              id: 'str-2',
              title: 'String Algorithms',
              description: 'Implement string matching and pattern algorithms',
              timeEstimate: '30 min',
              difficulty: 'Hard' as const,
              isCompleted: false,
              isLocked: true,
              type: 'practice' as const,
            },
          ],
        },
        {
          id: 'two-pointers',
          name: 'Two Pointers',
          articles: 1,
          problems: 16,
          topicStrength: 33,
          isUnlocked: false,
          isCompleted: false,
          icon: '👆',
          color: 'bg-teal-600',
          problemsList: [
            {
              id: 'tp-1',
              title: 'Two Pointer Basics',
              description: 'Learn two pointer technique fundamentals',
              timeEstimate: '15 min',
              difficulty: 'Easy' as const,
              isCompleted: false,
              isLocked: true,
              type: 'practice' as const,
            },
            {
              id: 'tp-2',
              title: 'Two Pointer Applications',
              description: 'Apply two pointer technique to solve problems',
              timeEstimate: '25 min',
              difficulty: 'Medium' as const,
              isCompleted: false,
              isLocked: true,
              type: 'practice' as const,
            },
          ],
        },
      ],

    },
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'text-green-400';
      case 'Medium': return 'text-yellow-400';
      case 'Hard': return 'text-red-400';
      default: return 'text-slate-400';
    }
  };

  const handleProblemClick = (problemId: string) => {
    console.log('Starting problem:', problemId);
    // Here you would navigate to the actual problem solving interface
    alert(`Starting problem: ${problemId}`);
  };



  // If a topic is selected, show the course structure
  if (selectedTopic === 'computer-architecture') {
    return (
      <CourseStructure
        topicName="Computer Architecture"
        levels={computerArchitectureLevels}
        onProblemClick={handleProblemClick}
      />
    );
  }

  // Show topics selection
  return (
    <div className="min-h-screen bg-slate-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-12 text-center">
          <h1 className="text-4xl font-bold text-white mb-4">Practice Topics</h1>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            Choose a topic to start practicing. Each topic contains comprehensive problems 
            designed to strengthen your electronics and semiconductor knowledge.
          </p>
        </div>

        {/* Topics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {topics.map((topic) => {
            const Icon = topic.icon;
            return (
              <div key={topic.id} className="card card-hover group">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div className={`w-16 h-16 bg-gradient-to-r ${topic.color} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200`}>
                      <Icon className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-white mb-2">{topic.name}</h3>
                      <div className="flex items-center space-x-4 text-sm">
                        <span className={`font-medium ${getDifficultyColor(topic.difficulty)}`}>
                          {topic.difficulty}
                        </span>
                        <span className="text-slate-400">
                          {topic.problemCount} problems
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <p className="text-slate-300 mb-6 text-lg leading-relaxed">
                  {topic.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm text-slate-400">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>Avg: {topic.avgTime}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Target className="w-4 h-4" />
                      <span>{topic.problemCount} problems</span>
                    </div>
                  </div>
                  
                  {topic.id === 'verilog-practice' ? (
                    <Link 
                      to="/verilog-practice"
                      className={`bg-gradient-to-r ${topic.color} hover:shadow-lg text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105`}
                      onClick={(e) => e.stopPropagation()}
                    >
                      Start Practice
                    </Link>
                  ) : topic.id === 'computer-architecture' ? (
                    <button
                      className={`bg-gradient-to-r ${topic.color} hover:shadow-lg text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105`}
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedTopic('computer-architecture');
                      }}
                    >
                      Start Practice
                    </button>
                  ) : (
                    <Link 
                      to={`/practice/${topic.id}`}
                      className={`bg-gradient-to-r ${topic.color} hover:shadow-lg text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105`}
                      onClick={(e) => e.stopPropagation()}
                    >
                      Start Practice
                    </Link>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Additional Info */}
        <div className="mt-16 text-center">
          <div className="card max-w-2xl mx-auto">
            <h3 className="text-xl font-semibold text-white mb-4">How Practice Works</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <BookOpen className="w-4 h-4 text-white" />
                </div>
                <div>
                  <div className="font-medium text-white">Learn Concepts</div>
                  <div className="text-slate-400">Understand theory first</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
                  <Target className="w-4 h-4 text-white" />
                </div>
                <div>
                  <div className="font-medium text-white">Solve Problems</div>
                  <div className="text-slate-400">Apply your knowledge</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg flex items-center justify-center">
                  <Star className="w-4 h-4 text-white" />
                </div>
                <div>
                  <div className="font-medium text-white">Track Progress</div>
                  <div className="text-slate-400">Monitor your growth</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Practice; 