import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, FileText, Code, Play, MessageSquare, Undo2, Bug } from 'lucide-react';

const VerilogPractice: React.FC = () => {
  const [activeTab, setActiveTab] = useState('question');

  const tabs = [
    { id: 'question', label: 'Question', icon: FileText },
    { id: 'solution', label: 'Solution', icon: Code },
    { id: 'video', label: 'Video', icon: Play },
    { id: 'discussion', label: 'Discussion', icon: MessageSquare },
  ];

  const verilogCode = `// RISC-V: Register File
// Designing the register file for YARP core.

module yarp_regfile (
    input logic clk,
    input logic reset_n,
    
    // Source registers
    input logic [4:0] rs1_addr_i,
    input logic [4:0] rs2_addr_i,
    
    // Destination register
    input logic [4:0] rd_addr_i,
    input logic wr_en_i,
    input logic [31:0] wr_data_i,
    
    // Register Data
    output logic [31:0] rs1_data_o,
    output logic [31:0] rs2_data_o
);

    // Implement register file as an 2D array
    logic [31:0] registers [31:0];
    
    // Read operations
    assign rs1_data_o = (rs1_addr_i == 5'b00000) ? 32'b0 : registers[rs1_addr_i];
    assign rs2_data_o = (rs2_addr_i == 5'b00000) ? 32'b0 : registers[rs2_addr_i];
    
    // Write operation
    always_ff @(posedge clk or negedge reset_n) begin
        if (!reset_n) begin
            for (int i = 0; i < 32; i++) begin
                registers[i] <= 32'b0;
            end
        end else if (wr_en_i && rd_addr_i != 5'b00000) begin
            registers[rd_addr_i] <= wr_data_i;
        end
    end

endmodule`;

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 px-8 py-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link
            to="/practice"
            className="text-gray-400 hover:text-white transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
              1
            </div>
            <h1 className="text-xl font-bold">YARP Register File</h1>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <span className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
            NOT SUBMITTED
          </span>
        </div>
      </div>
      
      {/* Main Split Layout */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Left Pane - Tutorial Content */}
        <div className="w-1/2 bg-blue-50 border-r border-blue-100 flex flex-col">
          {/* Tabs */}
          <div className="flex border-b border-blue-100">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-3 text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'text-blue-700 border-b-2 border-blue-700 bg-blue-100'
                      : 'text-blue-500 hover:text-blue-700'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </div>
          
          {/* Content Area */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="prose max-w-none">
              <h2 className="text-2xl font-bold mb-4 text-blue-900">YARP Register File</h2>
              
              <p className="text-blue-900 leading-relaxed mb-6">
                This tutorial would cover the register file for the YARP core. The previous tutorials talked about the RISC-V ISA and the instruction memory from where our processor would fetch the instruction and use the decode block to decode the 32-bit fetched instruction as per the encodings given in the ISA. Out of those components, almost every instruction had an encoding for the registers specified by the ISA. Those were decoded as either the source registers (rs1, rs2) or the destination register (rd). Both the source registers and destination registers refer to one of 32 available architectural registers as specified by the RISC-V ISA. This tutorial would cover how the processor maintains each of these registers and how these can be read to obtain the value stored in the source registers or to written to store a value into the destination register.
              </p>
              
              <h3 className="text-xl font-bold mb-4 text-blue-900">Register File</h3>
              
              <p className="text-blue-900 leading-relaxed mb-6">
                As discussed above the register file is used to read the value stored into one of the architectural registers and even to write the value to one of the architecture register. Here is the quick recap of the six instruction types as defined by the RISC-V ISA and the way those encode the source or destination registers:
              </p>
              
              <div className="overflow-x-auto mb-6">
                <table className="w-full border-collapse border border-blue-200 text-sm">
                  <thead>
                    <tr className="bg-blue-100">
                      <th className="border border-blue-200 p-2 text-left text-blue-900 font-medium">Instruction Type</th>
                      <th className="border border-blue-200 p-2 text-left text-blue-900 font-medium">Source Register 1 (RS1)</th>
                      <th className="border border-blue-200 p-2 text-left text-blue-900 font-medium">Source Register 2 (RS2)</th>
                      <th className="border border-blue-200 p-2 text-left text-blue-900 font-medium">Destination Register (RD)</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-blue-200">
                      <td className="border border-blue-200 p-2 text-blue-900">R-type</td>
                      <td className="border border-blue-200 p-2 text-blue-900">instr[19:15]</td>
                      <td className="border border-blue-200 p-2 text-blue-900">instr[24:20]</td>
                      <td className="border border-blue-200 p-2 text-blue-900">instr[11:7]</td>
                    </tr>
                    <tr className="border-b border-blue-200">
                      <td className="border border-blue-200 p-2 text-blue-900">I-type</td>
                      <td className="border border-blue-200 p-2 text-blue-900">instr[19:15]</td>
                      <td className="border border-blue-200 p-2 text-blue-900">-</td>
                      <td className="border border-blue-200 p-2 text-blue-900">instr[11:7]</td>
                    </tr>
                    <tr className="border-b border-blue-200">
                      <td className="border border-blue-200 p-2 text-blue-900">S-type</td>
                      <td className="border border-blue-200 p-2 text-blue-900">instr[19:15]</td>
                      <td className="border border-blue-200 p-2 text-blue-900">instr[24:20]</td>
                      <td className="border border-blue-200 p-2 text-blue-900">-</td>
                    </tr>
                    <tr className="border-b border-blue-200">
                      <td className="border border-blue-200 p-2 text-blue-900">B-type</td>
                      <td className="border border-blue-200 p-2 text-blue-900">instr[19:15]</td>
                      <td className="border border-blue-200 p-2 text-blue-900">instr[24:20]</td>
                      <td className="border border-blue-200 p-2 text-blue-900">-</td>
                    </tr>
                    <tr className="border-b border-blue-200">
                      <td className="border border-blue-200 p-2 text-blue-900">U-type</td>
                      <td className="border border-blue-200 p-2 text-blue-900">-</td>
                      <td className="border border-blue-200 p-2 text-blue-900">-</td>
                      <td className="border border-blue-200 p-2 text-blue-900">instr[11:7]</td>
                    </tr>
                    <tr className="border-b border-blue-200">
                      <td className="border border-blue-200 p-2 text-blue-900">J-type</td>
                      <td className="border border-blue-200 p-2 text-blue-900">-</td>
                      <td className="border border-blue-200 p-2 text-blue-900">-</td>
                      <td className="border border-blue-200 p-2 text-blue-900">instr[11:7]</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              <p className="text-blue-900 leading-relaxed">
                There are few instruction types which either don't read any source registers (U and J type) or don't write to the destination register (S and B type). The R-type instructions read the values from both the source register and write the result of the instruction to the destination register. The I-type instructions on the other hand just read the source register 1. The later tutorials would cover in-depth as to how these instruction compute their result.
              </p>
            </div>
          </div>
          
          {/* Bottom Buttons */}
          <div className="p-4 border-t border-blue-100 flex justify-between bg-blue-50 sticky bottom-0">
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
              All Modules
            </button>
            <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2">
              <Bug className="w-4 h-4" />
              Report a Bug
            </button>
          </div>
        </div>
        
        {/* Right Pane - Code Editor */}
        <div className="w-1/2 bg-gray-900 flex flex-col">
          {/* Editor Header */}
          <div className="bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button className="text-gray-400 hover:text-white transition-colors">
                <Undo2 className="w-4 h-4" />
              </button>
              <span className="text-gray-400 text-sm">&lt;/&gt;</span>
            </div>
            <select className="bg-gray-700 text-gray-300 px-3 py-1 rounded text-sm border border-gray-600">
              <option>Sublime</option>
              <option>Emacs</option>
              <option>Vim</option>
            </select>
          </div>
          
          {/* Code Editor */}
          <div className="flex-1 overflow-hidden">
            <div className="h-full bg-gray-900 p-4 font-mono text-sm overflow-auto">
              <div className="flex">
                {/* Line Numbers */}
                <div className="text-gray-500 pr-4 select-none">
                  {verilogCode.split('\n').map((_, index) => (
                    <div key={index} className="text-right">
                      {index + 1}
                    </div>
                  ))}
                </div>
                
                {/* Code Content */}
                <div className="flex-1">
                  {verilogCode.split('\n').map((line, index) => (
                    <div key={index} className="text-gray-300">
                      <span className="whitespace-pre">{line}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          {/* Bottom Buttons */}
          <div className="p-4 border-t border-gray-700 flex justify-end bg-gray-900 sticky bottom-0">
            <div className="flex gap-3">
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Run Testcases
              </button>
              <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Submit
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VerilogPractice; 