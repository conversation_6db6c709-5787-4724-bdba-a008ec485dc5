import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ChevronRight, ChevronDown, ChevronUp, ArrowLeft } from 'lucide-react';

const levels = [
  {
    id: 'level-1',
    name: 'Level 1',
    title: 'Time Complexity',
    unlocked: true,
    completed: false,
    subtopics: [
      {
        id: 'how-to-calculate-running-time',
        title: 'How to Calculate Running Time?',
        content: `When analyzing algorithms, calculating the running time is crucial for understanding performance characteristics. The running time of an algorithm depends on several factors including the size of the input, the specific input values, and the computational model being used.

To calculate running time, we typically follow these steps:

1. **Identify the basic operations**: Determine what constitutes a basic operation in your algorithm (e.g., comparisons, arithmetic operations, assignments).

2. **Count operations**: Analyze how many times each basic operation is executed as a function of the input size.

3. **Express as a function**: Write the total number of operations as a mathematical function of the input size n.

4. **Simplify**: Use asymptotic notation to express the growth rate of this function.

For example, in a simple linear search algorithm:
- We perform at most n comparisons
- Each comparison is a basic operation
- The running time is O(n) in the worst case

This systematic approach helps us compare different algorithms and choose the most efficient one for a given problem.`,
      },
      {
        id: 'asymptotic-notations',
        title: 'Asymptotic notations',
        content: `Asymptotic notation is a mathematical tool used to describe the performance of algorithms. It provides a way to express the growth rate of an algorithm's running time or space usage as the input size grows.

The three most common asymptotic notations are:

**Big O Notation (O)**: Represents the upper bound or worst-case scenario. If an algorithm has O(n²) complexity, it means the running time will not grow faster than n².

**Big Omega Notation (Ω)**: Represents the lower bound or best-case scenario. If an algorithm has Ω(n) complexity, it means the running time will not grow slower than n.

**Big Theta Notation (Θ)**: Represents both upper and lower bounds, giving us a tight bound. If an algorithm has Θ(n log n) complexity, it means the running time grows exactly at the rate of n log n.

Common complexity classes include:
- O(1): Constant time
- O(log n): Logarithmic time
- O(n): Linear time
- O(n log n): Linearithmic time
- O(n²): Quadratic time
- O(2ⁿ): Exponential time

Understanding these notations is essential for algorithm analysis and optimization.`,
      },
      {
        id: 'how-to-calculate-time-complexity',
        title: 'How to Calculate Time Complexity?',
        content: `Calculating time complexity involves analyzing how the running time of an algorithm grows with the input size. Here's a systematic approach:

**Step 1: Identify the Input Size**
- Determine what parameter represents the size of the input (n)
- This could be the length of an array, number of nodes in a tree, etc.

**Step 2: Count Basic Operations**
- Identify the basic operations in your algorithm
- Count how many times each operation is executed
- Focus on operations that contribute most to the running time

**Step 3: Express as a Function**
- Write the total count as a function of n
- Consider best, average, and worst cases

**Step 4: Simplify Using Asymptotic Notation**
- Remove lower-order terms
- Remove constant factors
- Express using Big O notation

**Example: Linear Search**
\`\`\`javascript
function linearSearch(arr, target) {
    for (let i = 0; i < arr.length; i++) {  // n iterations
        if (arr[i] === target) {             // 1 comparison per iteration
            return i;
        }
    }
    return -1;
}
\`\`\`
- Basic operation: comparison (arr[i] === target)
- Number of comparisons: at most n
- Time complexity: O(n)

**Common Patterns:**
- Single loop: O(n)
- Nested loops: O(n²)
- Divide and conquer: O(log n)
- Recursive algorithms: analyze recursion tree`,
      },
      {
        id: 'time-complexity-examples',
        title: 'Time Complexity Examples',
        content: `Let's explore various time complexity examples with different algorithms:

**O(1) - Constant Time**
\`\`\`javascript
function getFirstElement(arr) {
    return arr[0];  // Always one operation
}
\`\`\`
Accessing an array element by index is constant time.

**O(log n) - Logarithmic Time**
\`\`\`javascript
function binarySearch(arr, target) {
    let left = 0, right = arr.length - 1;
    while (left <= right) {
        let mid = Math.floor((left + right) / 2);
        if (arr[mid] === target) return mid;
        if (arr[mid] < target) left = mid + 1;
        else right = mid - 1;
    }
    return -1;
}
\`\`\`
Each iteration reduces the search space by half.

**O(n) - Linear Time**
\`\`\`javascript
function findMax(arr) {
    let max = arr[0];
    for (let i = 1; i < arr.length; i++) {
        if (arr[i] > max) max = arr[i];
    }
    return max;
}
\`\`\`
Must examine each element once.

**O(n²) - Quadratic Time**
\`\`\`javascript
function bubbleSort(arr) {
    for (let i = 0; i < arr.length; i++) {
        for (let j = 0; j < arr.length - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];
            }
        }
    }
}
\`\`\`
Nested loops result in quadratic complexity.

**O(n log n) - Linearithmic Time**
\`\`\`javascript
function mergeSort(arr) {
    if (arr.length <= 1) return arr;
    
    const mid = Math.floor(arr.length / 2);
    const left = mergeSort(arr.slice(0, mid));
    const right = mergeSort(arr.slice(mid));
    
    return merge(left, right);
}
\`\`\`
Divide and conquer algorithms often achieve this complexity.`,
      },
      {
        id: 'relevance-of-time-complexity',
        title: 'Relevance of time complexity',
        content: `Time complexity is crucial in software development for several important reasons:

**1. Performance Optimization**
Understanding time complexity helps developers choose the most efficient algorithms for their specific use cases. A poorly chosen algorithm can lead to significant performance issues, especially with large datasets.

**2. Scalability Planning**
Time complexity analysis helps predict how an application will perform as the input size grows. This is essential for planning system architecture and infrastructure requirements.

**3. Resource Management**
Efficient algorithms reduce CPU usage, memory consumption, and energy consumption. This is particularly important in resource-constrained environments like mobile devices or embedded systems.

**4. User Experience**
Slow algorithms can lead to poor user experience, especially in interactive applications where responsiveness is crucial. Users expect applications to respond quickly regardless of the data size.

**5. Cost Optimization**
In cloud computing environments, inefficient algorithms can lead to higher costs due to increased processing time and resource usage.

**6. Competitive Advantage**
Companies that can process data faster and more efficiently often have a competitive advantage in their respective markets.

**Real-World Examples:**
- **Search engines**: Must process millions of web pages quickly
- **E-commerce**: Need to search through large product catalogs
- **Social media**: Must handle millions of posts and interactions
- **Financial systems**: Process large volumes of transactions
- **Gaming**: Real-time algorithms for game mechanics

**When to Optimize:**
- When dealing with large datasets
- In performance-critical applications
- When building scalable systems
- When working with limited resources
- When user experience depends on speed

Remember: Premature optimization can be counterproductive. Always profile your application to identify actual bottlenecks before optimizing.`,
      },
      {
        id: 'space-complexity',
        title: 'Space Complexity',
        content: `Space complexity measures the amount of memory an algorithm uses as a function of the input size. It's equally important as time complexity for understanding algorithm efficiency.

**Types of Space Complexity:**

**O(1) - Constant Space**
\`\`\`javascript
function sumArray(arr) {
    let sum = 0;
    for (let i = 0; i < arr.length; i++) {
        sum += arr[i];
    }
    return sum;
}
\`\`\`
Uses a fixed amount of extra space regardless of input size.

**O(n) - Linear Space**
\`\`\`javascript
function reverseArray(arr) {
    let result = [];
    for (let i = arr.length - 1; i >= 0; i--) {
        result.push(arr[i]);
    }
    return result;
}
\`\`\`
Creates a new array of the same size as the input.

**O(n²) - Quadratic Space**
\`\`\`javascript
function generateMatrix(n) {
    let matrix = [];
    for (let i = 0; i < n; i++) {
        matrix[i] = [];
        for (let j = 0; j < n; j++) {
            matrix[i][j] = i * j;
        }
    }
    return matrix;
}
\`\`\`
Creates a 2D array of size n × n.

**Space vs Time Trade-offs:**
- **Caching**: Using more memory to store computed results
- **Lookup tables**: Pre-computing values for faster access
- **Compression**: Using algorithms to reduce storage requirements

**Auxiliary Space vs Total Space:**
- **Auxiliary space**: Extra space used by the algorithm
- **Total space**: Input space + auxiliary space

**Memory Management Considerations:**
- Stack space for recursive calls
- Heap space for dynamic allocations
- Cache efficiency and memory access patterns
- Garbage collection overhead

**Best Practices:**
1. Consider both time and space complexity
2. Profile memory usage in addition to time
3. Be aware of memory constraints in your target environment
4. Consider the impact of garbage collection
5. Optimize for the most common use cases`,
      },
    ],
  },
  {
    id: 'level-2',
    name: 'Level 2',
    title: 'Arrays',
    unlocked: false,
    completed: false,
    subtopics: [
      {
        id: 'intro-pointers',
        title: 'Introduction to pointers in C/C++',
        content: `Pointers are fundamental concepts in C/C++ programming that allow you to work directly with memory addresses. Understanding pointers is crucial for efficient programming and is often a key topic in technical interviews.

**What are Pointers?**
A pointer is a variable that stores the memory address of another variable. Instead of storing the actual value, it stores the location where the value is stored in memory.

**Basic Pointer Syntax:**
\`\`\`c
int number = 42;
int *ptr = &number;  // ptr stores the address of number
\`\`\`

**Key Concepts:**
- **Address operator (&)**: Gets the memory address of a variable
- **Dereference operator (*)**: Accesses the value at the memory address
- **Pointer arithmetic**: Allows you to navigate through memory

**Common Use Cases:**
- Dynamic memory allocation
- Passing by reference
- Array manipulation
- Function pointers

**Memory Management:**
\`\`\`c
int *dynamicArray = malloc(10 * sizeof(int));
// Use the array
free(dynamicArray);  // Always free allocated memory
\`\`\`

Understanding pointers is essential for:
- Efficient memory usage
- Performance optimization
- System programming
- Interview preparation`,
      },
      {
        id: 'arrays-fundamentals',
        title: 'Arrays in programming - fundamentals',
        content: `Arrays are one of the most fundamental data structures in programming. They provide a way to store multiple values of the same type in contiguous memory locations.

**Array Basics:**
\`\`\`c
int numbers[5] = {1, 2, 3, 4, 5};
\`\`\`

**Key Characteristics:**
- **Fixed size**: Once declared, size cannot be changed
- **Contiguous memory**: Elements are stored in adjacent memory locations
- **Index-based access**: Elements are accessed using indices (0-based)
- **Same data type**: All elements must be of the same type

**Common Operations:**
- **Access**: O(1) time complexity
- **Search**: O(n) time complexity (linear search)
- **Insertion**: O(n) time complexity (shifting required)
- **Deletion**: O(n) time complexity (shifting required)

**Multi-dimensional Arrays:**
\`\`\`c
int matrix[3][3] = {
    {1, 2, 3},
    {4, 5, 6},
    {7, 8, 9}
};
\`\`\`

**Advantages:**
- Fast access to elements
- Memory efficient
- Simple to implement

**Disadvantages:**
- Fixed size
- Insertion/deletion is expensive
- Memory allocation issues

Arrays form the foundation for more complex data structures and are essential for algorithm implementation.`,
      },
      {
        id: 'pointers-arrays',
        title: 'Pointers and arrays',
        content: `The relationship between pointers and arrays is fundamental in C/C++ programming. Understanding this relationship is crucial for efficient programming and is often tested in technical interviews.

**Array-Pointer Relationship:**
In C/C++, arrays and pointers are closely related. The name of an array is essentially a pointer to its first element.

\`\`\`c
int arr[5] = {1, 2, 3, 4, 5};
int *ptr = arr;  // arr is equivalent to &arr[0]
\`\`\`

**Pointer Arithmetic:**
\`\`\`c
int arr[5] = {1, 2, 3, 4, 5};
int *ptr = arr;

// These are equivalent:
arr[2] = 10;
*(ptr + 2) = 10;
\`\`\`

**Array as Function Parameter:**
\`\`\`c
void printArray(int arr[], int size) {
    for(int i = 0; i < size; i++) {
        printf("%d ", arr[i]);
    }
}

// Function call
int numbers[5] = {1, 2, 3, 4, 5};
printArray(numbers, 5);
\`\`\`

**Key Points:**
- Array name decays to pointer
- Pointer arithmetic works with arrays
- Arrays can be passed to functions
- Memory layout is contiguous

**Common Pitfalls:**
- Buffer overflows
- Dangling pointers
- Memory leaks
- Array bounds checking

This knowledge is essential for:
- System programming
- Performance optimization
- Memory management
- Interview preparation`,
      },
    ],
  },
  {
    id: 'level-3',
    name: 'Level 3',
    title: 'Binary Search',
    unlocked: false,
    completed: false,
    subtopics: [
      {
        id: 'binary-search-basics',
        title: 'Binary Search Algorithm',
        content: `Binary search is one of the most efficient searching algorithms, with a time complexity of O(log n). It works on sorted arrays and uses a divide-and-conquer approach.

**How Binary Search Works:**
1. Compare the target with the middle element
2. If they match, return the index
3. If target is less than middle, search left half
4. If target is greater than middle, search right half
5. Repeat until element is found or search space is empty

**Implementation:**
\`\`\`javascript
function binarySearch(arr, target) {
    let left = 0;
    let right = arr.length - 1;
    
    while (left <= right) {
        let mid = Math.floor((left + right) / 2);
        
        if (arr[mid] === target) {
            return mid;
        } else if (arr[mid] < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    
    return -1; // Element not found
}
\`\`\`

**Time Complexity:**
- **Best Case**: O(1) - Element found at middle
- **Average Case**: O(log n) - Element found after several iterations
- **Worst Case**: O(log n) - Element not found

**Space Complexity:**
- **Iterative**: O(1) - Constant extra space
- **Recursive**: O(log n) - Stack space for recursion

**Advantages:**
- Very efficient for large datasets
- Works well with sorted data
- Predictable performance

**Disadvantages:**
- Requires sorted data
- Not suitable for linked lists
- Complex implementation
- Applications:
- Finding elements in sorted arrays
- Range queries
- Optimization problems
- Game theory algorithms

Binary search is a fundamental algorithm that every programmer should master.`,
      },
      {
        id: 'binary-search-variations',
        title: 'Binary Search Variations',
        content: `Binary search has many variations that solve different problems efficiently. Understanding these variations is crucial for competitive programming and technical interviews.

**1. Finding First Occurrence:**
\`\`\`javascript
function findFirstOccurrence(arr, target) {
    let left = 0;
    let right = arr.length - 1;
    let result = -1;
    
    while (left <= right) {
        let mid = Math.floor((left + right) / 2);
        
        if (arr[mid] === target) {
            result = mid;
            right = mid - 1; // Continue searching left
        } else if (arr[mid] < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    
    return result;
}
\`\`\`

**2. Finding Last Occurrence:**
\`\`\`javascript
function findLastOccurrence(arr, target) {
    let left = 0;
    let right = arr.length - 1;
    let result = -1;
    
    while (left <= right) {
        let mid = Math.floor((left + right) / 2);
        
        if (arr[mid] === target) {
            result = mid;
            left = mid + 1; // Continue searching right
        } else if (arr[mid] < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    
    return result;
}
\`\`\`

**3. Finding Ceiling:**
\`\`\`javascript
function findCeiling(arr, target) {
    let left = 0;
    let right = arr.length - 1;
    
    while (left <= right) {
        let mid = Math.floor((left + right) / 2);
        
        if (arr[mid] === target) {
            return mid;
        } else if (arr[mid] < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    
    return left; // Ceiling is at left position
}
\`\`\`

**4. Finding Floor:**
\`\`\`javascript
function findFloor(arr, target) {
    let left = 0;
    let right = arr.length - 1;
    
    while (left <= right) {
        let mid = Math.floor((left + right) / 2);
        
        if (arr[mid] === target) {
            return mid;
        } else if (arr[mid] < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    
    return right; // Floor is at right position
}
\`\`\`

**Key Insights:**
- Always consider edge cases
- Handle duplicates properly
- Understand the search space
- Know when to stop
      
These variations are commonly asked in interviews and are essential for competitive programming.`,
      },
    ],
  },
];

const TimeComplexity: React.FC = () => {
  const [selectedLevel, setSelectedLevel] = useState(0);
  const [selectedSubtopic, setSelectedSubtopic] = useState(0);
  const [expandedLevels, setExpandedLevels] = useState<number[]>([0]); // Level 1 is expanded by default

  const currentLevel = levels[selectedLevel];
  const currentSubtopic = currentLevel.subtopics[selectedSubtopic];

  const toggleLevel = (levelIndex: number) => {
    setExpandedLevels(prev => 
      prev.includes(levelIndex) 
        ? prev.filter(i => i !== levelIndex)
        : [...prev, levelIndex]
    );
  };

  const handleLevelClick = (levelIndex: number) => {
    setSelectedLevel(levelIndex);
    setSelectedSubtopic(0); // Reset subtopic selection when level changes
    if (!expandedLevels.includes(levelIndex)) {
      setExpandedLevels(prev => [...prev, levelIndex]); // Expand if not already expanded
    }
  };

  const handleSubtopicClick = (subtopicIndex: number) => {
    setSelectedSubtopic(subtopicIndex);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 px-8 py-6 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link
            to="/practice"
            className="text-gray-400 hover:text-white transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Time Complexity</h1>
          </div>
        </div>
        <Link
          to="/practice/time-complexity"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors"
        >
          Go to Problems
        </Link>
      </div>
      
      {/* Main Content */}
      <div className="flex mt-8 gap-6">
        {/* Sidebar */}
        <aside className="w-64 bg-gray-800 border-r border-gray-700 p-4 flex-shrink-0 h-fit sticky top-8 max-h-[calc(100vh-120px)] overflow-y-auto">
          <div className="mb-6">
            <div className="flex items-center gap-2 text-gray-400 text-sm font-bold mb-4">
              <span>Contents</span>
              <ArrowLeft className="w-4 h-4" />
              <ArrowLeft className="w-4 h-4" />
            </div>
            
            {levels.map((level, levelIndex) => (
              <div key={level.id} className="mb-4">
                <button
                  onClick={() => toggleLevel(levelIndex)}
                  className={`flex items-center justify-between w-full text-left py-2 px-2 rounded-lg transition-all duration-200 font-semibold text-sm ${
                    selectedLevel === levelIndex
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-300 hover:bg-gray-700'
                  }`}
                >
                  <span>{level.name}</span>
                  {expandedLevels.includes(levelIndex) ? (
                    <ChevronUp className="w-4 h-4" />
                  ) : (
                    <ChevronDown className="w-4 h-4" />
                  )}
                </button>
                
                {expandedLevels.includes(levelIndex) && (
                  <div className="ml-1 mt-2 border-l-2 border-gray-600 pl-2">
                    <div className="font-semibold text-gray-200 mb-2">{level.title}</div>
                    <div className="space-y-1">
                      {level.subtopics.map((subtopic, subtopicIndex) => (
                        <button
                          key={subtopic.id}
                          onClick={() => {
                            handleLevelClick(levelIndex);
                            handleSubtopicClick(subtopicIndex);
                          }}
                          className={`flex items-center gap-2 text-left py-1 px-1 rounded transition-all duration-200 text-xs ${
                            selectedLevel === levelIndex && selectedSubtopic === subtopicIndex
                              ? 'bg-blue-600 text-white font-semibold' 
                              : 'text-gray-400 hover:bg-gray-700 hover:text-gray-200'
                          }`}
                        >
                          <span className={`w-2 h-2 rounded-full border ${
                            selectedLevel === levelIndex && selectedSubtopic === subtopicIndex
                              ? 'bg-blue-500 border-blue-500' 
                              : 'border-gray-500'
                          }`}></span>
                          <span className="line-clamp-2">{subtopic.title}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
            
            <Link
              to="/practice/time-complexity"
              className="mt-6 flex items-center gap-2 text-blue-400 font-semibold hover:text-blue-300 hover:underline text-sm transition-colors"
            >
              Go to problems <ChevronRight className="w-4 h-4" />
            </Link>
            
            {/* Level 2 Section */}
            <div className="mt-8">
              <div className="text-gray-400 text-sm font-bold mb-2">LEVEL 2</div>
              <button className="w-full bg-gray-700 hover:bg-gray-600 text-gray-300 py-2 px-2 rounded-lg transition-colors flex items-center gap-2">
                <span>🔒</span>
                <span>Jump to Level 2</span>
              </button>
            </div>
          </div>
        </aside>
        
        {/* Main Theory Content */}
        <main className="flex-1 bg-gray-800 p-8 min-h-[600px]">
          <h2 className="text-3xl font-bold mb-4 text-white">Time Complexity Of A Computer Program</h2>
          <h3 className="text-xl font-semibold mb-6 text-gray-300">What is Time complexity and What is its need?</h3>
          
          <div className="text-gray-300 leading-relaxed text-base space-y-4">
            <p>
              Imagine you are playing a game where you have to guess a number between 1 and 1000. 
              The person who knows the number will only tell you if your guess is "too big" or "too small".
            </p>
            
            <p>Now, there are two ways to approach this:</p>
            
            <ul className="list-disc list-inside space-y-2 ml-4">
              <li>We can guess each number from 1 to 1000, and see if it is correct.</li>
              <li>We can pick the middle number, if he says 'too big' then we know for sure that the number is on the left side so we can discard the right side, similarly if he says 'too small' we can discard the left side. We can repeat the same process until he says it's correct.</li>
            </ul>
            
            <p className="font-semibold">Which way do you think is better?</p>
            
            <p>
              The second way is much better! In the worst case, it will take only 10 guesses compared to 1000 guesses in the first way.
            </p>
            
            <p>
              In computer science, we say that the time complexity of the first way is <strong>O(n)</strong> and that of the second way is <strong>O(log n)</strong>.
            </p>
            
            <p>
              In computer programming, there are multiple approaches to solve the same problem. 
              These approaches can be different in terms of time, space, and computational power. 
              So, we need to have a way to distinguish between efficient and inefficient algorithms.
            </p>
            
            <p>
              The concept of time complexity was introduced to compare algorithms written in different languages or run on different machines.
            </p>
            
            <p className="font-semibold">
              By definition, Time complexity is the time taken by an algorithm/program to run as a function of the length of the input.
            </p>
            
            <h4 className="text-lg font-semibold mt-6 mb-3 text-white">Why is it so important?</h4>
            <ul className="list-disc list-inside space-y-2 ml-4">
              <li>It can clearly distinguish between two different algorithms based on their efficiency.</li>
              <li>It's independent of the machine on which the algorithm is run.</li>
            </ul>
          </div>
          
          {/* Questions Section */}
          <div className="mt-12">
            <h3 className="text-2xl font-bold mb-6 text-white">Practice Questions</h3>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Topic 1: Basic Time Complexity */}
              <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    4/4
                  </div>
                  <h4 className="text-lg font-semibold text-gray-200">Basic Time Complexity</h4>
                  <span className="text-gray-400 text-lg">ⓘ</span>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between py-2 border-b border-gray-600">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                        ✓
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-200 font-medium">TIME_CALC1</span>
                          <span className="text-blue-400 text-xs">a</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-gray-400 text-sm">2:15 Mins</div>
                      <div className="text-green-400 text-sm font-medium">20/20 Pts</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between py-2 border-b border-gray-600">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                        ✓
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-200 font-medium">ASYMPTOTIC_NOTATION</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-gray-400 text-sm">4:32 Mins</div>
                      <div className="text-green-400 text-sm font-medium">30/30 Pts</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between py-2 border-b border-gray-600">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                        ✓
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-200 font-medium">COMPLEXITY_ANALYSIS</span>
                          <span className="text-blue-400 text-xs">a</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-gray-400 text-sm">6:18 Mins</div>
                      <div className="text-green-400 text-sm font-medium">50/50 Pts</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                        ✓
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-200 font-medium">SPACE_COMPLEXITY</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-gray-400 text-sm">3:45 Mins</div>
                      <div className="text-green-400 text-sm font-medium">40/40 Pts</div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Topic 2: Advanced Analysis */}
              <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    3/3
                  </div>
                  <h4 className="text-lg font-semibold text-gray-200">Advanced Analysis</h4>
                  <span className="text-gray-400 text-lg">ⓘ</span>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between py-2 border-b border-gray-600">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                        ✓
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-200 font-medium">RECURSION_COMPLEXITY</span>
                          <span className="text-blue-400 text-xs">a</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-gray-400 text-sm">8:12 Mins</div>
                      <div className="text-green-400 text-sm font-medium">75/80 Pts</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between py-2 border-b border-gray-600">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                        ✓
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-200 font-medium">AMORTIZED_ANALYSIS</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-gray-400 text-sm">5:30 Mins</div>
                      <div className="text-green-400 text-sm font-medium">45/50 Pts</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                        ✓
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-200 font-medium">OPTIMIZATION_PROBLEMS</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-gray-400 text-sm">7:25 Mins</div>
                      <div className="text-green-400 text-sm font-medium">90/100 Pts</div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Topic 3: Mathematical Foundations */}
              <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    2/2
                  </div>
                  <h4 className="text-lg font-semibold text-gray-200">Mathematical Foundations</h4>
                  <span className="text-gray-400 text-lg">ⓘ</span>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between py-2 border-b border-gray-600">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                        ✓
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-200 font-medium">MATH_PROOFS</span>
                          <span className="text-blue-400 text-xs">a</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-gray-400 text-sm">4:15 Mins</div>
                      <div className="text-green-400 text-sm font-medium">60/60 Pts</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                        ✓
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-200 font-medium">COMPLEXITY_CLASSES</span>
                          <div className="w-3 h-3 bg-gray-500 rounded"></div>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-gray-400 text-sm">6:45 Mins</div>
                      <div className="text-green-400 text-sm font-medium">80/80 Pts</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Bonus Section */}
            <div className="mt-8">
              <div className="bg-blue-600 rounded-lg p-4 flex items-center gap-3 max-w-md">
                <span className="text-white text-lg">🔓</span>
                <span className="text-white font-semibold">Topic Bonus Unlocked!</span>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default TimeComplexity; 