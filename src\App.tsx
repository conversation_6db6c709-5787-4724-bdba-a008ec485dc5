import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Home from './pages/Home';
import Practice from './pages/Practice';
import Contest from './pages/Contest';
import AboutUs from './pages/AboutUs';
import Login from './pages/Login';
import TimeComplexity from './pages/TimeComplexity';
import VerilogPractice from './pages/VerilogPractice';
import DigitalElectronicsTest from './pages/DigitalElectronicsTest';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <Header />
        <main>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/practice" element={<Practice />} />
            <Route path="/contest" element={<Contest />} />
            <Route path="/about" element={<AboutUs />} />
            <Route path="/login" element={<Login />} />
            <Route path="/time-complexity" element={<TimeComplexity />} />
            <Route path="/verilog-practice" element={<VerilogPractice />} />
            <Route path="/contest/digital-electronics-mastery/test" element={<DigitalElectronicsTest />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
