import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Lock, CheckCircle, Play, Clock, Star, Gift, Key, Info, Target, TrendingUp } from 'lucide-react';
import { Link } from 'react-router-dom';

interface Problem {
  id: string;
  title: string;
  description: string;
  timeEstimate: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  isCompleted: boolean;
  isLocked: boolean;
  type: 'practice' | 'checkpoint' | 'bonus';
}

interface Subtopic {
  id: string;
  name: string;
  articles: number;
  problems: number;
  topicStrength: number; // percentage
  isUnlocked: boolean;
  isCompleted: boolean;
  problemsList: Problem[];
  icon: string;
  color: string;
}

interface Level {
  id: string;
  name: string;
  description: string;
  subtopics: Subtopic[];
  isUnlocked: boolean;
  isCompleted: boolean;
}

interface CourseStructureProps {
  topicName: string;
  levels: Level[];
  onProblemClick: (problemId: string) => void;
}

const CourseStructure: React.FC<CourseStructureProps> = ({ topicName, levels, onProblemClick }) => {
  const [expandedSubtopics, setExpandedSubtopics] = useState<string[]>([]);

  const toggleSubtopic = (subtopicId: string) => {
    setExpandedSubtopics(prev => 
      prev.includes(subtopicId) 
        ? prev.filter(id => id !== subtopicId)
        : [...prev, subtopicId]
    );
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'text-green-400';
      case 'Medium': return 'text-yellow-400';
      case 'Hard': return 'text-red-400';
      default: return 'text-slate-400';
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">{topicName}</h1>
          <p className="text-xl text-slate-300">Master electronics through structured learning paths</p>
        </div>

        {/* Main Content with Sidebar */}
        <div className="flex gap-8">
          {/* Main Content - Tree Structure */}
          <div className="flex-1">
            <div className="space-y-12">
              {levels.map((level, levelIndex) => {
                const isLastLevel = levelIndex === levels.length - 1;
                
                return (
                  <div key={level.id} className="relative">
                    {/* Level Node - Oval Design */}
                    <div className="flex justify-center mb-8">
                      <div className="bg-slate-700 border-2 border-slate-500 rounded-full px-8 py-3 shadow-lg">
                        <h2 className="text-xl font-bold text-white text-center">Level {levelIndex + 1}</h2>
                      </div>
                    </div>

                    {/* Subtopic Cards */}
                    {level.subtopics.length === 1 ? (
                      <div className="flex justify-center mb-8">
                        {level.subtopics.map((subtopic, subtopicIndex) => {
                          const isExpanded = expandedSubtopics.includes(subtopic.id);
                          return (
                            <div key={subtopic.id} className="relative w-full max-w-md">
                              {/* Connection Line from Level */}
                              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-0.5 h-12 bg-slate-500"></div>
                              {/* Subtopic Card - InterviewBit Style */}
                              <div className="bg-gradient-to-br from-blue-600 to-blue-700 border border-blue-500 rounded-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 relative overflow-hidden">
                              {/* Binary Pattern Background */}
                              <div className="absolute inset-0 opacity-10">
                                <div className="absolute top-2 right-2 text-xs text-white/30 font-mono">1010</div>
                                <div className="absolute top-6 right-2 text-xs text-white/30 font-mono">1101</div>
                                <div className="absolute top-10 right-2 text-xs text-white/30 font-mono">0110</div>
                              </div>
                              
                              {/* Card Header */}
                              <div className="flex items-center justify-between mb-4 relative z-10">
                                <div className="flex items-center space-x-3">
                                  <div className={`w-10 h-10 rounded-full flex items-center justify-center bg-white/20 backdrop-blur-sm`}>
                                    <span className="text-white font-bold text-lg">{subtopic.icon}</span>
                                  </div>
                                  <div>
                                    <h3 className="text-lg font-semibold text-white">{subtopic.name}</h3>
                                    <div className="flex items-center space-x-4 text-sm text-blue-100">
                                      <span>{subtopic.articles} Articles</span>
                                      <span>{subtopic.problems} Problems</span>
                                    </div>
                                  </div>
                                </div>
                                
                                {/* Progress Circle */}
                                <div className="relative">
                                  <div className="w-12 h-12 rounded-full border-4 border-white/30 flex items-center justify-center bg-white/10 backdrop-blur-sm">
                                    <span className="text-xs font-bold text-white">{subtopic.topicStrength}%</span>
                                  </div>
                                  {subtopic.isCompleted && (
                                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                      <CheckCircle className="w-4 h-4 text-white" />
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Star Rating */}
                              <div className="flex items-center justify-center mb-4">
                                {[1, 2, 3].map((star) => (
                                  <Star 
                                    key={star}
                                    className={`w-5 h-5 ${
                                      star <= Math.ceil(subtopic.topicStrength / 33) 
                                        ? 'text-yellow-300 fill-current' 
                                        : 'text-white/30'
                                    }`}
                                  />
                                ))}
                              </div>

                              {/* Action Buttons */}
                              <div className="flex items-center justify-between">
                                <button 
                                  className="flex items-center space-x-2 text-sm text-blue-100 hover:text-white transition-colors"
                                  onClick={() => toggleSubtopic(subtopic.id)}
                                >
                                  {isExpanded ? (
                                    <ChevronDown className="w-4 h-4" />
                                  ) : (
                                    <ChevronRight className="w-4 h-4" />
                                  )}
                                  <span>View Problems</span>
                                </button>
                                
                                {subtopic.isUnlocked ? (
                                  subtopic.id === 'time-complexity' ? (
                                    <Link to="/time-complexity" className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors backdrop-blur-sm">
                                      Start Practice
                                    </Link>
                                  ) : (
                                    <button className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors backdrop-blur-sm">
                                      Start Practice
                                    </button>
                                  )
                                ) : (
                                  <button className="bg-slate-600 text-slate-400 px-4 py-2 rounded-lg text-sm font-medium cursor-not-allowed">
                                    Locked
                                  </button>
                                )}
                              </div>

                              {/* Problems List */}
                              {isExpanded && (
                                <div className="mt-4 space-y-2">
                                  {subtopic.problemsList.map((problem) => (
                                    <div 
                                      key={problem.id} 
                                      className={`p-3 rounded-lg border transition-all duration-200 ${
                                        problem.isLocked 
                                          ? 'bg-slate-800/50 border-slate-600 cursor-not-allowed' 
                                          : 'bg-white/10 border-white/20 hover:bg-white/20 cursor-pointer backdrop-blur-sm'
                                      }`}
                                      onClick={() => !problem.isLocked && onProblemClick(problem.id)}
                                    >
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                          <div className={`w-5 h-5 rounded-full flex items-center justify-center ${
                                            problem.isCompleted 
                                              ? 'bg-green-500' 
                                              : problem.isLocked 
                                              ? 'bg-slate-600' 
                                              : 'bg-blue-500'
                                          }`}>
                                            {problem.isCompleted ? (
                                              <CheckCircle className="w-3 h-3 text-white" />
                                            ) : problem.isLocked ? (
                                              <Lock className="w-2 h-2 text-slate-400" />
                                            ) : (
                                              <Play className="w-2 h-2 text-white" />
                                            )}
                                          </div>
                                          <div>
                                            <h5 className="font-medium text-white text-sm">{problem.title}</h5>
                                            <p className="text-xs text-blue-100">{problem.description}</p>
                                          </div>
                                        </div>
                                        
                                        <div className="flex items-center space-x-3 text-xs">
                                          <div className="flex items-center space-x-1 text-blue-100">
                                            <Clock className="w-3 h-3" />
                                            <span>{problem.timeEstimate}</span>
                                          </div>
                                          <div className={`font-medium ${getDifficultyColor(problem.difficulty)}`}>
                                            {problem.difficulty}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                            {/* Lock/Gift Icon */}
                            <div className="flex justify-center mt-4">
                              {subtopic.isUnlocked ? (
                                <Gift className="w-6 h-6 text-teal-400" />
                              ) : (
                                <Lock className="w-6 h-6 text-slate-400" />
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        {level.subtopics.map((subtopic, subtopicIndex) => {
                          const isExpanded = expandedSubtopics.includes(subtopic.id);
                          return (
                            <div key={subtopic.id} className="relative">
                              {/* Connection Line from Level */}
                              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-0.5 h-12 bg-slate-500"></div>
                              {/* Subtopic Card - InterviewBit Style */}
                              <div className="bg-gradient-to-br from-blue-600 to-blue-700 border border-blue-500 rounded-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 relative overflow-hidden">
                              {/* Binary Pattern Background */}
                              <div className="absolute inset-0 opacity-10">
                                <div className="absolute top-2 right-2 text-xs text-white/30 font-mono">1010</div>
                                <div className="absolute top-6 right-2 text-xs text-white/30 font-mono">1101</div>
                                <div className="absolute top-10 right-2 text-xs text-white/30 font-mono">0110</div>
                              </div>
                              
                              {/* Card Header */}
                              <div className="flex items-center justify-between mb-4 relative z-10">
                                <div className="flex items-center space-x-3">
                                  <div className={`w-10 h-10 rounded-full flex items-center justify-center bg-white/20 backdrop-blur-sm`}>
                                    <span className="text-white font-bold text-lg">{subtopic.icon}</span>
                                  </div>
                                  <div>
                                    <h3 className="text-lg font-semibold text-white">{subtopic.name}</h3>
                                    <div className="flex items-center space-x-4 text-sm text-blue-100">
                                      <span>{subtopic.articles} Articles</span>
                                      <span>{subtopic.problems} Problems</span>
                                    </div>
                                  </div>
                                </div>
                                
                                {/* Progress Circle */}
                                <div className="relative">
                                  <div className="w-12 h-12 rounded-full border-4 border-white/30 flex items-center justify-center bg-white/10 backdrop-blur-sm">
                                    <span className="text-xs font-bold text-white">{subtopic.topicStrength}%</span>
                                  </div>
                                  {subtopic.isCompleted && (
                                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                      <CheckCircle className="w-4 h-4 text-white" />
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Star Rating */}
                              <div className="flex items-center justify-center mb-4">
                                {[1, 2, 3].map((star) => (
                                  <Star 
                                    key={star}
                                    className={`w-5 h-5 ${
                                      star <= Math.ceil(subtopic.topicStrength / 33) 
                                        ? 'text-yellow-300 fill-current' 
                                        : 'text-white/30'
                                    }`}
                                  />
                                ))}
                              </div>

                              {/* Action Buttons */}
                              <div className="flex items-center justify-between">
                                <button 
                                  className="flex items-center space-x-2 text-sm text-blue-100 hover:text-white transition-colors"
                                  onClick={() => toggleSubtopic(subtopic.id)}
                                >
                                  {isExpanded ? (
                                    <ChevronDown className="w-4 h-4" />
                                  ) : (
                                    <ChevronRight className="w-4 h-4" />
                                  )}
                                  <span>View Problems</span>
                                </button>
                                
                                {subtopic.isUnlocked ? (
                                  subtopic.id === 'time-complexity' ? (
                                    <Link to="/time-complexity" className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors backdrop-blur-sm">
                                      Start Practice
                                    </Link>
                                  ) : (
                                    <button className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors backdrop-blur-sm">
                                      Start Practice
                                    </button>
                                  )
                                ) : (
                                  <button className="bg-slate-600 text-slate-400 px-4 py-2 rounded-lg text-sm font-medium cursor-not-allowed">
                                    Locked
                                  </button>
                                )}
                              </div>

                              {/* Problems List */}
                              {isExpanded && (
                                <div className="mt-4 space-y-2">
                                  {subtopic.problemsList.map((problem) => (
                                    <div 
                                      key={problem.id} 
                                      className={`p-3 rounded-lg border transition-all duration-200 ${
                                        problem.isLocked 
                                          ? 'bg-slate-800/50 border-slate-600 cursor-not-allowed' 
                                          : 'bg-white/10 border-white/20 hover:bg-white/20 cursor-pointer backdrop-blur-sm'
                                      }`}
                                      onClick={() => !problem.isLocked && onProblemClick(problem.id)}
                                    >
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                          <div className={`w-5 h-5 rounded-full flex items-center justify-center ${
                                            problem.isCompleted 
                                              ? 'bg-green-500' 
                                              : problem.isLocked 
                                              ? 'bg-slate-600' 
                                              : 'bg-blue-500'
                                          }`}>
                                            {problem.isCompleted ? (
                                              <CheckCircle className="w-3 h-3 text-white" />
                                            ) : problem.isLocked ? (
                                              <Lock className="w-2 h-2 text-slate-400" />
                                            ) : (
                                              <Play className="w-2 h-2 text-white" />
                                            )}
                                          </div>
                                          <div>
                                            <h5 className="font-medium text-white text-sm">{problem.title}</h5>
                                            <p className="text-xs text-blue-100">{problem.description}</p>
                                          </div>
                                        </div>
                                        
                                        <div className="flex items-center space-x-3 text-xs">
                                          <div className="flex items-center space-x-1 text-blue-100">
                                            <Clock className="w-3 h-3" />
                                            <span>{problem.timeEstimate}</span>
                                          </div>
                                          <div className={`font-medium ${getDifficultyColor(problem.difficulty)}`}>
                                            {problem.difficulty}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                            {/* Lock/Gift Icon */}
                            <div className="flex justify-center mt-4">
                              {subtopic.isUnlocked ? (
                                <Gift className="w-6 h-6 text-teal-400" />
                              ) : (
                                <Lock className="w-6 h-6 text-slate-400" />
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                    )}

                    {/* Jump to Next Level Button */}
                    {!isLastLevel && (
                      <div className="flex flex-col items-center mb-0">
                        {/* Connection Line */}
                        <div className="w-0.5 h-8 bg-slate-500 mb-4"></div>
                        {/* Jump Button - InterviewBit Style */}
                        <button className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-3 shadow-lg mb-0">
                          <Key className="w-5 h-5" />
                          <span>Jump to Level {levelIndex + 2}</span>
                          <Info className="w-5 h-5" />
                        </button>
                        {/* Only a single vertical line to next level, no horizontal branch */}
                        <div className="w-0.5 h-8 bg-slate-500"></div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="w-80 flex-shrink-0">
            <div className="bg-slate-800 rounded-xl p-6 shadow-lg sticky top-8">
              {/* Upcoming Achievements */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-white mb-4">UPCOMING ACHIEVEMENTS</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Lock className="w-4 h-4 text-slate-400" />
                      <span className="text-sm text-white">Two Pointers Captain</span>
                    </div>
                    <span className="text-sm text-green-400">11/16</span>
                  </div>
                  <div className="w-full bg-slate-700 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '69%' }}></div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Lock className="w-4 h-4 text-slate-400" />
                      <span className="text-sm text-white">Arrays Master</span>
                    </div>
                    <span className="text-sm text-green-400">26/44</span>
                  </div>
                  <div className="w-full bg-slate-700 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '59%' }}></div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Lock className="w-4 h-4 text-slate-400" />
                      <span className="text-sm text-white">Bit Manipulation Pro</span>
                    </div>
                    <span className="text-sm text-green-400">5/11</span>
                  </div>
                  <div className="w-full bg-slate-700 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '45%' }}></div>
                  </div>
                </div>
              </div>

              {/* Daily Goal */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-white mb-4">DAILY GOAL</h3>
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">0</div>
                    <div className="text-xs text-slate-400">Day's Score</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">0</div>
                    <div className="text-xs text-slate-400">Day Streak</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">10</div>
                    <div className="text-xs text-slate-400">Hours Left</div>
                  </div>
                </div>
                
                {/* Weekly Progress Chart */}
                <div className="space-y-2">
                  <div className="flex justify-between text-xs text-slate-400">
                    <span>Sun</span>
                    <span>Mon</span>
                    <span>Tue</span>
                    <span>Wed</span>
                    <span>Thu</span>
                    <span>Fri</span>
                    <span>Sat</span>
                  </div>
                  <div className="relative h-8 bg-slate-700 rounded-lg">
                    <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-orange-500 transform -translate-y-1/2"></div>
                    <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-slate-600 transform -translate-y-1/2"></div>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-orange-400">Daily Goal</span>
                    <span className="text-blue-400">Achieved Goal</span>
                  </div>
                </div>
              </div>

              {/* Your Progress */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">YOUR PROGRESS</h3>
                <div className="flex justify-center mb-4">
                  <div className="relative w-24 h-24">
                    <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                      <path
                        className="text-slate-700"
                        stroke="currentColor"
                        strokeWidth="3"
                        fill="none"
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      />
                      <path
                        className="text-green-500"
                        stroke="currentColor"
                        strokeWidth="3"
                        strokeDasharray="35, 100"
                        strokeDashoffset="22.75"
                        fill="none"
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-lg font-bold text-white">35%</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-green-400">Easy</span>
                    <span className="text-white">94 / 275</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-yellow-400">Medium</span>
                    <span className="text-white">113 / 287</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-red-400">Hard</span>
                    <span className="text-white">11 / 53</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseStructure; 