@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-slate-900 text-slate-100;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105;
  }
  
  .btn-secondary {
    @apply bg-slate-700 hover:bg-slate-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg;
  }
  
  .btn-accent {
    @apply bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105;
  }
  
  .input-field {
    @apply bg-slate-800 border border-slate-600 text-white placeholder-slate-400 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }
  
  .card {
    @apply bg-slate-800 border border-slate-700 rounded-xl p-6 shadow-xl backdrop-blur-sm bg-opacity-90;
  }
  
  .card-hover {
    @apply hover:bg-slate-700 hover:border-slate-600 transition-all duration-200 transform hover:scale-105;
  }
  
  .gradient-bg {
    @apply bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent;
  }
  
  .gradient-border {
    @apply border border-transparent bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-border;
  }
}
